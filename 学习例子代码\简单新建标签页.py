#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DrissionPage简单示例：新建标签页
功能：快速新建标签页并访问Temu商品列表页面
"""

from DrissionPage import Chromium

def simple_new_tab():
    """
    最简单的新建标签页示例
    直接新建标签页并访问指定网址
    """
    print("🚀 开始新建标签页...")
    
    # 连接到9222端口的浏览器（如果没有会自动启动）
    browser = Chromium(9222)
    print("✅ 已连接到浏览器")
    
    # 新建标签页并访问目标网址
    target_url = "https://agentseller.temu.com/goods/list"
    print(f"🌐 正在访问：{target_url}")
    
    # 一步完成：新建标签页 + 访问网址
    new_tab = browser.new_tab(url=target_url)
    
    # 激活新标签页（显示在前台）
    new_tab.set.activate()
    
    print("✅ 新标签页创建完成！")
    print(f"📄 页面标题：{new_tab.title}")
    print(f"🔗 当前网址：{new_tab.url}")
    
    return new_tab

if __name__ == "__main__":
    """
    主程序：执行新建标签页操作
    """
    print("=" * 50)
    print("📚 DrissionPage新建标签页学习")
    print("=" * 50)
    
    try:
        # 执行新建标签页操作
        tab = simple_new_tab()
        
        if tab:
            print("\n🎉 操作成功完成！")
            print("💡 新标签页已在浏览器中打开")
        
    except Exception as e:
        print(f"❌ 发生错误：{e}")
        print("💡 请确保浏览器可以正常启动")
    
    print("=" * 50)
