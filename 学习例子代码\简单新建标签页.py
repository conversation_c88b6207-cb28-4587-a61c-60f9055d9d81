#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DrissionPage网络监控示例：新建标签页并抓包
功能：新建标签页、监控网络请求、抓取所有网络数据包信息
"""

from DrissionPage import Chromium
import time

def monitor_network_and_create_tab():
    """
    新建标签页并监控网络请求的完整示例
    包含页面加载等待和全部网络信息抓取
    """
    print("🚀 开始新建标签页并启动网络监控...")

    # 连接到9222端口的浏览器（如果没有会自动启动）
    browser = Chromium(9222)
    print("✅ 已连接到浏览器")

    # 新建一个空白标签页（不立即访问网址）
    new_tab = browser.new_tab()
    print("📄 新建空白标签页完成")

    # 激活新标签页（显示在前台）
    new_tab.set.activate()
    print("🎯 标签页已激活")

    # 启动网络监听器 - 监听所有网络请求
    print("🔍 启动网络监听器...")
    new_tab.listen.start(targets=True)  # True表示监听所有网络请求
    print("✅ 网络监听器已启动，开始监控所有网络数据包")

    # 访问目标网址
    target_url = "https://agentseller.temu.com/goods/list"
    print(f"🌐 开始访问目标网址：{target_url}")

    # 记录开始时间
    start_time = time.time()

    # 访问网址
    new_tab.get(target_url)

    print("⏳ 等待页面开始加载...")
    new_tab.wait.load_start()  # 等待页面开始加载
    print("✅ 页面开始加载")

    print("⏳ 等待文档加载完成...")
    new_tab.wait.doc_loaded()  # 等待文档加载完成
    print("✅ 文档加载完成")

    # 等待所有网络请求完成（包括异步请求）
    print("⏳ 等待所有网络请求完成...")
    new_tab.listen.wait_silent(timeout=10)  # 等待10秒内所有请求完成
    print("✅ 所有网络请求已完成")

    # 计算加载时间
    load_time = time.time() - start_time
    print(f"⏱️ 页面总加载时间：{load_time:.2f}秒")

    return new_tab

def capture_and_analyze_packets(tab):
    """
    抓取和分析网络数据包
    获取所有网络请求的详细信息
    """
    print("\n" + "=" * 80)
    print("📦 开始抓取和分析网络数据包")
    print("=" * 80)

    # 获取所有已捕获的数据包
    print("🔍 正在获取所有网络数据包...")

    # 使用steps()方法获取所有数据包
    packets = []
    try:
        # 设置一个短暂的超时来获取已有的数据包
        for packet in tab.listen.steps(timeout=1):
            packets.append(packet)
    except:
        # 超时是正常的，表示没有更多数据包了
        pass

    print(f"📊 总共捕获到 {len(packets)} 个网络数据包")

    if not packets:
        print("⚠️ 没有捕获到网络数据包，可能需要重新加载页面")
        return

    # 分析每个数据包
    for i, packet in enumerate(packets, 1):
        print(f"\n📦 数据包 #{i}")
        print("-" * 60)

        # 基本信息
        print(f"🔗 URL: {packet.url}")
        print(f"📋 请求方法: {packet.method}")
        print(f"📂 资源类型: {packet.resourceType}")
        print(f"❌ 是否失败: {packet.is_failed}")

        # 请求信息
        if packet.request:
            print(f"\n📤 请求信息:")
            print(f"   Headers数量: {len(packet.request.headers) if packet.request.headers else 0}")

            # 显示重要的请求头
            if packet.request.headers:
                important_headers = ['User-Agent', 'Referer', 'Accept', 'Content-Type']
                for header in important_headers:
                    if header in packet.request.headers:
                        value = packet.request.headers[header]
                        # 截断过长的值
                        if len(value) > 100:
                            value = value[:100] + "..."
                        print(f"   {header}: {value}")

            # POST数据
            if packet.request.postData:
                print(f"   POST数据: {str(packet.request.postData)[:200]}...")

        # 响应信息
        if packet.response:
            print(f"\n📥 响应信息:")
            print(f"   状态码: {packet.response.status}")
            print(f"   状态文本: {packet.response.statusText}")
            print(f"   Headers数量: {len(packet.response.headers) if packet.response.headers else 0}")

            # 显示重要的响应头
            if packet.response.headers:
                important_headers = ['Content-Type', 'Content-Length', 'Set-Cookie']
                for header in important_headers:
                    if header in packet.response.headers:
                        value = packet.response.headers[header]
                        if len(value) > 100:
                            value = value[:100] + "..."
                        print(f"   {header}: {value}")

            # 响应体信息
            if hasattr(packet.response, 'body') and packet.response.body:
                body_preview = str(packet.response.body)[:200]
                print(f"   响应体预览: {body_preview}...")

        # 失败信息
        if packet.is_failed and packet.fail_info:
            print(f"\n❌ 失败信息:")
            print(f"   错误文本: {packet.fail_info.errorText}")
            print(f"   是否取消: {packet.fail_info.canceled}")
            if packet.fail_info.blockedReason:
                print(f"   拦截原因: {packet.fail_info.blockedReason}")

    # 统计信息
    print(f"\n" + "=" * 80)
    print("� 网络请求统计信息")
    print("=" * 80)

    # 按请求方法统计
    method_count = {}
    for packet in packets:
        method = packet.method
        method_count[method] = method_count.get(method, 0) + 1

    print("📊 按请求方法统计:")
    for method, count in method_count.items():
        print(f"   {method}: {count}个")

    # 按资源类型统计
    resource_count = {}
    for packet in packets:
        resource_type = packet.resourceType
        resource_count[resource_type] = resource_count.get(resource_type, 0) + 1

    print("\n📊 按资源类型统计:")
    for resource_type, count in resource_count.items():
        print(f"   {resource_type}: {count}个")

    # 按状态码统计
    status_count = {}
    for packet in packets:
        if packet.response and packet.response.status:
            status = packet.response.status
            status_count[status] = status_count.get(status, 0) + 1

    print("\n📊 按HTTP状态码统计:")
    for status, count in status_count.items():
        print(f"   {status}: {count}个")

    # 失败请求统计
    failed_count = sum(1 for packet in packets if packet.is_failed)
    print(f"\n❌ 失败请求: {failed_count}个")

    return packets

def save_packets_to_file(packets, filename="网络数据包分析.txt"):
    """
    将网络数据包信息保存到文件
    方便后续分析和查看
    """
    if not packets:
        print("⚠️ 没有数据包可保存")
        return

    print(f"💾 正在保存网络数据包信息到文件：{filename}")

    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=" * 100 + "\n")
            f.write("DrissionPage网络数据包抓取分析报告\n")
            f.write("=" * 100 + "\n\n")

            f.write(f"总数据包数量：{len(packets)}\n")
            f.write(f"生成时间：{time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # 详细数据包信息
            for i, packet in enumerate(packets, 1):
                f.write(f"\n{'='*60}\n")
                f.write(f"数据包 #{i}\n")
                f.write(f"{'='*60}\n")

                f.write(f"URL: {packet.url}\n")
                f.write(f"请求方法: {packet.method}\n")
                f.write(f"资源类型: {packet.resourceType}\n")
                f.write(f"是否失败: {packet.is_failed}\n")

                # 请求信息
                if packet.request:
                    f.write(f"\n--- 请求信息 ---\n")
                    if packet.request.headers:
                        f.write("请求头:\n")
                        for key, value in packet.request.headers.items():
                            f.write(f"  {key}: {value}\n")

                    if packet.request.postData:
                        f.write(f"POST数据: {packet.request.postData}\n")

                # 响应信息
                if packet.response:
                    f.write(f"\n--- 响应信息 ---\n")
                    f.write(f"状态码: {packet.response.status}\n")
                    f.write(f"状态文本: {packet.response.statusText}\n")

                    if packet.response.headers:
                        f.write("响应头:\n")
                        for key, value in packet.response.headers.items():
                            f.write(f"  {key}: {value}\n")

                    if hasattr(packet.response, 'body') and packet.response.body:
                        body_str = str(packet.response.body)
                        if len(body_str) > 1000:
                            body_str = body_str[:1000] + "\n... (内容过长，已截断)"
                        f.write(f"响应体:\n{body_str}\n")

                # 失败信息
                if packet.is_failed and packet.fail_info:
                    f.write(f"\n--- 失败信息 ---\n")
                    f.write(f"错误文本: {packet.fail_info.errorText}\n")
                    f.write(f"是否取消: {packet.fail_info.canceled}\n")
                    if packet.fail_info.blockedReason:
                        f.write(f"拦截原因: {packet.fail_info.blockedReason}\n")

        print(f"✅ 网络数据包信息已保存到：{filename}")

    except Exception as e:
        print(f"❌ 保存文件时发生错误：{e}")

if __name__ == "__main__":
    """
    主程序：执行新建标签页和网络监控操作
    """
    print("=" * 80)
    print("📚 DrissionPage网络监控和抓包学习示例")
    print("=" * 80)

    try:
        # 步骤1：新建标签页并监控网络
        print("🚀 第一步：新建标签页并启动网络监控")
        tab = monitor_network_and_create_tab()

        if tab:
            print("\n✅ 标签页创建和网络监控完成！")
            print(f"📄 最终页面标题：{tab.title}")
            print(f"🔗 最终页面URL：{tab.url}")

            # 步骤2：抓取和分析网络数据包
            print("\n🚀 第二步：抓取和分析网络数据包")
            packets = capture_and_analyze_packets(tab)

            if packets:
                # 步骤3：保存数据包信息到文件
                print("\n🚀 第三步：保存网络数据包信息")
                save_packets_to_file(packets)

                print("\n🎉 所有操作完成！")
                print("� 操作总结：")
                print(f"   ✅ 成功新建标签页并访问目标网址")
                print(f"   ✅ 监控到 {len(packets)} 个网络数据包")
                print(f"   ✅ 详细分析了所有网络请求和响应")
                print(f"   ✅ 保存了完整的抓包信息到文件")

                # 停止监听器
                tab.listen.stop()
                print("   ✅ 网络监听器已停止")
            else:
                print("⚠️ 没有捕获到网络数据包")
        else:
            print("❌ 标签页创建失败")

    except Exception as e:
        print(f"❌ 程序执行过程中发生错误：{e}")
        print("💡 请检查：")
        print("   1. 浏览器是否正确安装并可以启动")
        print("   2. 网络连接是否正常")
        print("   3. 目标网址是否可访问")
        print("   4. DrissionPage库是否正确安装")

    print("\n" + "=" * 80)
    print("📚 DrissionPage网络监控学习完成")
    print("💡 查看生成的'网络数据包分析.txt'文件了解详细抓包信息")
    print("=" * 80)
